import { useState } from 'react'
import './App.css'

function App() {
  // Enhanced book data with more details
  const [books, setBooks] = useState([
    {
      id: 1,
      title: "To Kill a Mockingbird",
      author: "<PERSON> Lee",
      isAvailable: true,
      genre: "Fiction",
      year: 1960,
      isbn: "978-0-06-112008-4",
      description: "A gripping tale of racial injustice and childhood innocence in the American South."
    },
    {
      id: 2,
      title: "1984",
      author: "<PERSON>",
      isAvailable: false,
      genre: "Dystopian Fiction",
      year: 1949,
      isbn: "978-0-452-28423-4",
      description: "A dystopian social science fiction novel about totalitarian control."
    },
    {
      id: 3,
      title: "Pride and Prejudice",
      author: "Jane <PERSON>",
      isAvailable: true,
      genre: "Romance",
      year: 1813,
      isbn: "978-0-14-143951-8",
      description: "A romantic novel of manners set in Georgian England."
    },
    {
      id: 4,
      title: "The Great Gatsby",
      author: "<PERSON><PERSON>",
      isAvailable: true,
      genre: "Fiction",
      year: 1925,
      isbn: "978-0-7432-7356-5",
      description: "A critique of the American Dream set in the Jazz Age."
    },
    {
      id: 5,
      title: "Animal Farm",
      author: "<PERSON>",
      isAvailable: false,
      genre: "Political Satire",
      year: 1945,
      isbn: "978-0-452-28424-1",
      description: "An allegorical novella about farm animals who rebel against their human farmer."
    },
    {
      id: 6,
      title: "Jane Eyre",
      author: "Charlotte Brontë",
      isAvailable: true,
      genre: "Gothic Fiction",
      year: 1847,
      isbn: "978-0-14-144114-6",
      description: "A bildungsroman following the experiences of its eponymous heroine."
    },
    {
      id: 7,
      title: "The Catcher in the Rye",
      author: "J.D. Salinger",
      isAvailable: true,
      genre: "Coming-of-age",
      year: 1951,
      isbn: "978-0-316-76948-0",
      description: "A controversial novel about teenage rebellion and alienation."
    },
    {
      id: 8,
      title: "Lord of the Flies",
      author: "William Golding",
      isAvailable: false,
      genre: "Allegorical Fiction",
      year: 1954,
      isbn: "978-0-571-05686-2",
      description: "A story about a group of British boys stranded on an uninhabited island."
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState('all') // all, available, borrowed
  const [sortBy, setSortBy] = useState('title') // title, author, year

  // Filter and sort books
  const filteredBooks = books
    .filter(book => {
      const matchesSearch = book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           book.genre.toLowerCase().includes(searchTerm.toLowerCase())

      if (filterBy === 'available') return matchesSearch && book.isAvailable
      if (filterBy === 'borrowed') return matchesSearch && !book.isAvailable
      return matchesSearch
    })
    .sort((a, b) => {
      if (sortBy === 'title') return a.title.localeCompare(b.title)
      if (sortBy === 'author') return a.author.localeCompare(b.author)
      if (sortBy === 'year') return b.year - a.year
      return 0
    })

  // Handle borrowing a book
  const handleBorrow = (bookId) => {
    setBooks(books.map(book =>
      book.id === bookId ? { ...book, isAvailable: false } : book
    ))
  }

  // Handle returning a book
  const handleReturn = (bookId) => {
    setBooks(books.map(book =>
      book.id === bookId ? { ...book, isAvailable: true } : book
    ))
  }

  const availableCount = books.filter(book => book.isAvailable).length
  const borrowedCount = books.length - availableCount

  return (
    <div className="library-app">
      {/* Hero Section */}
      <header className="hero-section">
        <div className="hero-content">
          <div className="hero-icon">📚</div>
          <h1 className="hero-title">Library Management System</h1>
          <p className="hero-subtitle">Discover, borrow, and manage your favorite books</p>

          {/* Stats Cards */}
          <div className="stats-container">
            <div className="stat-card">
              <div className="stat-number">{books.length}</div>
              <div className="stat-label">Total Books</div>
            </div>
            <div className="stat-card available">
              <div className="stat-number">{availableCount}</div>
              <div className="stat-label">Available</div>
            </div>
            <div className="stat-card borrowed">
              <div className="stat-number">{borrowedCount}</div>
              <div className="stat-label">Borrowed</div>
            </div>
          </div>
        </div>
      </header>

      {/* Search and Filter Section */}
      <div className="controls-section">
        <div className="search-container">
          <div className="search-wrapper">
            <svg className="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              type="text"
              placeholder="Search by title, author, or genre..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        <div className="filters-container">
          <div className="filter-group">
            <label htmlFor="filter">Filter by:</label>
            <select
              id="filter"
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Books</option>
              <option value="available">Available Only</option>
              <option value="borrowed">Borrowed Only</option>
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="sort">Sort by:</label>
            <select
              id="sort"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="filter-select"
            >
              <option value="title">Title</option>
              <option value="author">Author</option>
              <option value="year">Year</option>
            </select>
          </div>
        </div>
      </div>

      {/* Books Section */}
      <main className="books-section">
        <div className="section-header">
          <h2 className="section-title">
            {filterBy === 'all' ? 'All Books' :
             filterBy === 'available' ? 'Available Books' : 'Borrowed Books'}
            <span className="results-count">({filteredBooks.length})</span>
          </h2>
        </div>

        {filteredBooks.length > 0 ? (
          <div className="books-grid">
            {filteredBooks.map(book => (
              <div key={book.id} className={`book-card ${!book.isAvailable ? 'borrowed' : 'available'}`}>
                <div className="book-header">
                  <div className="book-genre">{book.genre}</div>
                  <div className="book-year">{book.year}</div>
                </div>

                <div className="book-content">
                  <h3 className="book-title">{book.title}</h3>
                  <p className="book-author">by {book.author}</p>
                  <p className="book-description">{book.description}</p>
                </div>

                <div className="book-footer">
                  <div className="book-status">
                    {book.isAvailable ? (
                      <>
                        <span className="status-indicator available"></span>
                        <span className="status-text">Available</span>
                      </>
                    ) : (
                      <>
                        <span className="status-indicator borrowed"></span>
                        <span className="status-text">Borrowed</span>
                      </>
                    )}
                  </div>

                  {book.isAvailable ? (
                    <button
                      onClick={() => handleBorrow(book.id)}
                      className="action-btn borrow-btn"
                    >
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                        <path d="M2 17l10 5 10-5"></path>
                        <path d="M2 12l10 5 10-5"></path>
                      </svg>
                      Borrow Book
                    </button>
                  ) : (
                    <button
                      onClick={() => handleReturn(book.id)}
                      className="action-btn return-btn"
                    >
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M3 3l18 18"></path>
                        <path d="M9 9v10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2l2 2z"></path>
                        <path d="M17 3a2 2 0 012 2v6l-2-2V5a2 2 0 00-2-2"></path>
                      </svg>
                      Return Book
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">🔍</div>
            <h3>No books found</h3>
            <p>Try adjusting your search terms or filters</p>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
