import { useState } from "react";
import "./App.css";

function App() {
  // Indian books dataset with images
  const [books, setBooks] = useState([
    {
      id: 1,
      title: "The White Tiger",
      author: "<PERSON><PERSON><PERSON>",
      genre: "Fiction",
      year: 2008,
      isAvailable: true,
      image: "https://m.media-amazon.com/images/I/81VStYnDGrL.jpg",
      description:
        "Booker Prize-winning novel about modern India's class struggle."
    },
    {
      id: 2,
      title: "Train to Pakistan",
      author: "<PERSON><PERSON><PERSON><PERSON>",
      genre: "Historical Fiction",
      year: 1956,
      isAvailable: false,
      image: "https://m.media-amazon.com/images/I/71f0po3scLL.jpg",
      description:
        "A gripping tale set during the Partition of India in 1947."
    },
    {
      id: 3,
      title: "God of Small Things",
      author: "<PERSON><PERSON><PERSON><PERSON>",
      genre: "Literary Fiction",
      year: 1997,
      isAvailable: true,
      image: "https://m.media-amazon.com/images/I/81vpsIs58WL.jpg",
      description:
        "Booker Prize-winning story of family, caste and forbidden love in Kerala."
    },
    {
      id: 4,
      title: "Wings of Fire",
      author: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      genre: "Autobiography",
      year: 1999,
      isAvailable: true,
      image: "https://m.media-amazon.com/images/I/71KKZlVjbwL.jpg",
      description:
        "Autobiography of Dr. Kalam, India's Missile Man and former President."
    },
    {
      id: 5,
      title: "The Discovery of India",
      author: "Jawaharlal Nehru",
      genre: "History",
      year: 1946,
      isAvailable: false,
      image: "https://m.media-amazon.com/images/I/71R3SgCRF1L.jpg",
      description:
        "A detailed account of India's rich culture and history, written in prison."
    }
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState("all"); // all, available, borrowed
  const [sortBy, setSortBy] = useState("title"); // title, author, year

  // Filter + sort logic
  const filteredBooks = books
    .filter((book) => {
      const matches =
        book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
        book.genre.toLowerCase().includes(searchTerm.toLowerCase());

      if (filterBy === "available") return matches && book.isAvailable;
      if (filterBy === "borrowed") return matches && !book.isAvailable;
      return matches;
    })
    .sort((a, b) => {
      if (sortBy === "title") return a.title.localeCompare(b.title);
      if (sortBy === "author") return a.author.localeCompare(b.author);
      if (sortBy === "year") return b.year - a.year;
      return 0;
    });

  // Borrow / return actions
  const handleBorrow = (id) =>
    setBooks(books.map((b) => (b.id === id ? { ...b, isAvailable: false } : b)));
  const handleReturn = (id) =>
    setBooks(books.map((b) => (b.id === id ? { ...b, isAvailable: true } : b)));

  // Stats
  const availableCount = books.filter((b) => b.isAvailable).length;
  const borrowedCount = books.length - availableCount;

  return (
    <div className="library-app">
      {/* Header */}
      <header className="hero-section">
        <h1 className="hero-title">📚 Indian Library System</h1>
        <p className="hero-subtitle">
          Browse, borrow, and manage Indian classics and modern books
        </p>

        {/* Stats */}
        <div className="stats-container">
          <div className="stat-card">
            <div className="stat-number">{books.length}</div>
            <div className="stat-label">Total Books</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{availableCount}</div>
            <div className="stat-label">Available</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{borrowedCount}</div>
            <div className="stat-label">Borrowed</div>
          </div>
        </div>
      </header>

      {/* Controls */}
      <div className="controls-section">
        <input
          type="text"
          className="search-input"
          placeholder="Search by title, author, or genre..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />

        <div>
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Books</option>
            <option value="available">Available Only</option>
            <option value="borrowed">Borrowed Only</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="filter-select"
            style={{ marginLeft: "0.5rem" }}
          >
            <option value="title">Sort by Title</option>
            <option value="author">Sort by Author</option>
            <option value="year">Sort by Year</option>
          </select>
        </div>
      </div>

      {/* Books */}
      <main>
        <h2 style={{ marginBottom: "1rem" }}>
          {filterBy === "all"
            ? "All Books"
            : filterBy === "available"
            ? "Available Books"
            : "Borrowed Books"}{" "}
          ({filteredBooks.length})
        </h2>

        {filteredBooks.length > 0 ? (
          <div className="books-grid">
            {filteredBooks.map((book) => (
              <div
                key={book.id}
                className={`book-card ${!book.isAvailable ? "borrowed" : ""}`}
              >
                {/* Book Cover */}
                <img
                  src={book.image}
                  alt={book.title}
                  className="book-cover"
                />

                <div className="book-info">
                  <div className="book-header">
                    <span>{book.genre}</span>
                    <span>{book.year}</span>
                  </div>
                  <h3 className="book-title">{book.title}</h3>
                  <p className="book-author">by {book.author}</p>
                  <p className="book-description">{book.description}</p>

                  <div className="book-status">
                    <span
                      className={`status-indicator ${
                        book.isAvailable ? "available" : "borrowed"
                      }`}
                    ></span>
                    <span>
                      {book.isAvailable ? "Available" : "Borrowed"}
                    </span>
                  </div>

                  {book.isAvailable ? (
                    <button
                      className="action-btn borrow-btn"
                      onClick={() => handleBorrow(book.id)}
                    >
                      Borrow
                    </button>
                  ) : (
                    <button
                      className="action-btn return-btn"
                      onClick={() => handleReturn(book.id)}
                    >
                      Return
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <h3>No books found</h3>
            <p>Try adjusting your search or filters.</p>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
