import { useState } from 'react'
import './App.css'

function App() {
  // Sample book data
  const [books, setBooks] = useState([
    {
      id: 1,
      title: "To Kill a Mockingbird",
      author: "<PERSON> Lee",
      isAvailable: true
    },
    {
      id: 2,
      title: "1984",
      author: "<PERSON>",
      isAvailable: false
    },
    {
      id: 3,
      title: "Pride and Prejudice",
      author: "<PERSON>",
      isAvailable: true
    },
    {
      id: 4,
      title: "The Great Gatsby",
      author: "<PERSON><PERSON>",
      isAvailable: true
    },
    {
      id: 5,
      title: "Animal Farm",
      author: "<PERSON>",
      isAvailable: false
    },
    {
      id: 6,
      title: "<PERSON>",
      author: "<PERSON>",
      isAvailable: true
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')

  // Filter books based on search term (title or author)
  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    book.author.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Handle borrowing a book
  const handleBorrow = (bookId) => {
    setBooks(books.map(book =>
      book.id === bookId ? { ...book, isAvailable: false } : book
    ))
  }

  // Handle returning a book
  const handleReturn = (bookId) => {
    setBooks(books.map(book =>
      book.id === bookId ? { ...book, isAvailable: true } : book
    ))
  }

  return (
    <div className="library-app">
      <header className="header">
        <h1>📚 Library Management System</h1>
        <p>Manage your book collection with ease</p>
      </header>

      <div className="search-container">
        <input
          type="text"
          placeholder="Search by title or author..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="search-input"
        />
      </div>

      <div className="books-container">
        <h2>Available Books ({filteredBooks.filter(book => book.isAvailable).length})</h2>
        <div className="books-grid">
          {filteredBooks.map(book => (
            <div key={book.id} className={`book-card ${!book.isAvailable ? 'borrowed' : ''}`}>
              <h3 className="book-title">{book.title}</h3>
              <p className="book-author">by {book.author}</p>
              <div className="book-status">
                {book.isAvailable ? (
                  <div>
                    <span className="status-badge available">Available</span>
                    <button
                      onClick={() => handleBorrow(book.id)}
                      className="borrow-btn"
                    >
                      Borrow Book
                    </button>
                  </div>
                ) : (
                  <div>
                    <span className="status-badge borrowed">Borrowed</span>
                    <button
                      onClick={() => handleReturn(book.id)}
                      className="return-btn"
                    >
                      Return Book
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredBooks.length === 0 && (
          <p className="no-results">No books found matching your search.</p>
        )}
      </div>
    </div>
  )
}

export default App
